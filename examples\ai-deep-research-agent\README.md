# Deep Research Agent with Motia Framework

A powerful research assistant that leverages the Motia Framework to perform comprehensive web research on any topic and any question.

## Features

* **Deep Web Research**: Automatically searches the web, extracts content, and synthesizes findings
* **Iterative Research Process**: Supports multiple layers of research depth for comprehensive exploration
* **Event-Driven Architecture**: Built using Motia Framework's event system for robust workflow management
* **Parallel Processing**: Efficiently processes search results and content extraction
* **API Endpoints**: REST API access for initiating research and retrieving reports
* **Stateful Processing**: Maintains research state throughout the entire process

## How It Works

The Deep Research Agent works through a series of event-driven steps:

1. **Research Initiation**: Submit a research query via the API
2. **Query Generation**: Generate specific search queries based on the research topic
3. **Web Search**: Perform web searches using Firecrawl to find relevant content
4. **Content Extraction**: Extract and process content from the found web pages
5. **Analysis**: Analyze the extracted content to generate insights
6. **Follow-up Research**: Optionally perform deeper research based on initial findings
7. **Report Compilation**: Generate a comprehensive research report
8. **Result Retrieval**: Access the final report via API

## Prerequisites

* Node.js 18 or later
* **Option 1**: OpenAI API key + Firecrawl API key (hosted services)
* **Option 2**: Custom OpenAI-compatible API (local LLMs, Ollama, etc.) + Firecrawl API key
* **Option 3**: OpenAI API key + Local Firecrawl instance
* **Option 4**: Custom OpenAI-compatible API + Local Firecrawl instance (fully self-hosted)

## Setup

1. Clone this repository:
```
git clone <repository-url>
cd ai_deep_research_agent
```

2. Install dependencies:
```
npm install
```

3. Copy the example environment file and configure your API keys:
```
cp .env.example .env
```

4. Configure your environment based on your preferred setup (see Configuration Options below)

5. Start the Motia development server:
```
npm run dev
```

6. Access the Motia Workbench in your browser at `http://localhost:3000`

## Configuration Options

The Deep Research Agent supports multiple configuration options to accommodate different deployment scenarios. Choose the setup that best fits your needs:

### Option 1: Standard Setup (OpenAI + Firecrawl APIs)

This is the default configuration using hosted services:

```bash
# Required
OPENAI_API_KEY=your-openai-api-key-here
FIRECRAWL_API_KEY=your-firecrawl-api-key-here

# Optional
OPENAI_MODEL=gpt-4o
```

### Option 2: Custom OpenAI-Compatible API + Firecrawl API

Use local LLMs, Ollama, or other OpenAI-compatible providers:

```bash
# Custom OpenAI-compatible API configuration
OPENAI_BASE_URL=http://localhost:11434/v1  # Ollama example
OPENAI_MODEL=llama3.1:8b                   # Model name for your provider
OPENAI_API_KEY=not-required-for-ollama     # Some providers don't need this

# Firecrawl (hosted)
FIRECRAWL_API_KEY=your-firecrawl-api-key-here

# Optional: Additional headers for custom authentication
# OPENAI_DEFAULT_HEADERS={"Authorization": "Bearer your-token"}
```

### Option 3: OpenAI API + Local Firecrawl

Use OpenAI with a self-hosted Firecrawl instance:

```bash
# OpenAI (hosted)
OPENAI_API_KEY=your-openai-api-key-here
OPENAI_MODEL=gpt-4o

# Local Firecrawl configuration
FIRECRAWL_LOCAL_MODE=true
FIRECRAWL_API_URL=http://localhost:3002  # Your local Firecrawl instance
```

### Option 4: Fully Self-Hosted (Custom API + Local Firecrawl)

Complete self-hosted setup:

```bash
# Custom OpenAI-compatible API
OPENAI_BASE_URL=http://localhost:11434/v1
OPENAI_MODEL=llama3.1:8b
OPENAI_API_KEY=not-required

# Local Firecrawl
FIRECRAWL_LOCAL_MODE=true
FIRECRAWL_API_URL=http://localhost:3002
```

### Setting Up Local Firecrawl

To run Firecrawl locally, follow these steps:

1. Clone the Firecrawl repository:
```bash
git clone https://github.com/mendableai/firecrawl.git
cd firecrawl
```

2. Follow the setup instructions in the Firecrawl repository to run it locally
3. By default, Firecrawl runs on `http://localhost:3002`
4. Set `FIRECRAWL_LOCAL_MODE=true` in your `.env` file

### Setting Up Ollama (Example)

To use Ollama as your OpenAI-compatible provider:

1. Install Ollama from https://ollama.ai
2. Pull a model: `ollama pull llama3.1:8b`
3. Start Ollama: `ollama serve`
4. Configure your `.env`:
```bash
OPENAI_BASE_URL=http://localhost:11434/v1
OPENAI_MODEL=llama3.1:8b
OPENAI_API_KEY=not-required
```

## Project Structure

```
.
├── steps/                  # Motia step definitions
│   ├── research-api.step.ts        # API endpoint to start research
│   ├── status-api.step.ts          # API endpoint to check research status
│   ├── report-api.step.ts          # API endpoint to get research report
│   ├── generate-queries.step.ts    # Generate search queries from topic
│   ├── search-web.step.ts          # Perform web searches
│   ├── extract-content.step.ts     # Extract content from search results
│   ├── analyze-content.step.ts     # Analyze extracted content
│   ├── follow-up-research.step.ts  # Perform deeper research
│   └── compile-report.step.ts      # Compile final research report
├── services/               # External service integrations
│   ├── openai.service.ts           # OpenAI API integration
│   └── firecrawl.service.ts        # Firecrawl API integration
├── .env.example            # Example environment variables
├── package.json            # Project dependencies
└── tsconfig.json           # TypeScript configuration
```

## API Usage

### Start Research

```
POST /research
Content-Type: application/json

{
  "query": "The research topic or question",
  "breadth": 4,  // Number of search queries to generate (1-10)
  "depth": 2     // Depth of research iterations (1-5)
}
```

Response:
```json
{
  "message": "Research process started",
  "requestId": "unique-trace-id"
}
```

### Check Research Status

```
GET /research/status?requestId=unique-trace-id
```

Response:
```json
{
  "message": "Research status retrieved successfully",
  "requestId": "unique-trace-id",
  "originalQuery": "The research topic or question",
  "status": "in-progress",
  "progress": {
    "currentDepth": 1,
    "totalDepth": 2,
    "percentComplete": 50
  },
  "reportAvailable": false
}
```

### Get Research Report

```
GET /research/report?requestId=unique-trace-id
```

Response:
```json
{
  "message": "Research report retrieved successfully",
  "report": {
    "title": "Research Report Title",
    "overview": "Executive summary...",
    "sections": [
      {
        "title": "Section Title",
        "content": "Section content..."
      }
    ],
    "keyTakeaways": [
      "Key takeaway 1",
      "Key takeaway 2"
    ],
    "sources": [
      {
        "title": "Source Title",
        "url": "Source URL"
      }
    ],
    "originalQuery": "The research topic or question",
    "metadata": {
      "depthUsed": 2,
      "completedAt": "2025-03-18T16:45:30Z"
    }
  },
  "requestId": "unique-trace-id"
}
```

## Event Flow

The research process follows this event flow:

```
research-api → research-started → generate-queries → search-queries-generated → search-web → 
search-results-collected → extract-content → content-extracted → analyze-content → 
[analysis-completed OR follow-up-research-needed] → 
[compile-report OR follow-up-research → search-queries-generated] → report-completed
```

## Technologies Used

- **Motia Framework**: Event-driven architecture for workflow orchestration
- **OpenAI API / Custom LLMs**: For generating queries, analyzing content, and creating reports
  - Supports OpenAI's hosted API
  - Compatible with local LLMs (Ollama, LM Studio, etc.)
  - Any OpenAI-compatible API endpoint
- **Firecrawl**: Web search and content extraction
  - Supports hosted Firecrawl API service
  - Compatible with self-hosted Firecrawl instances
- **TypeScript**: Type-safe development
- **Zod**: Runtime validation for API requests and responses

## License

MIT License 