// Simple test script to test the research API
const http = require('http');

function testAPI() {
  const postData = JSON.stringify({
    query: 'test query about AI',
    breadth: 2,
    depth: 1
  });

  const options = {
    hostname: 'localhost',
    port: 3000,
    path: '/api/research',
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
      'Content-Length': Buffer.byteLength(postData)
    }
  };

  const req = http.request(options, (res) => {
    console.log(`Status: ${res.statusCode}`);
    console.log(`Headers: ${JSON.stringify(res.headers)}`);

    let data = '';
    res.on('data', (chunk) => {
      data += chunk;
    });

    res.on('end', () => {
      console.log('Response:', data);
    });
  });

  req.on('error', (error) => {
    console.error('Error testing API:', error);
  });

  req.write(postData);
  req.end();
}

testAPI();
