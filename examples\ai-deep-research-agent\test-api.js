// Simple test script to test the research API
const http = require('http');

function makeRequest(path, method = 'GET', postData = null) {
  return new Promise((resolve, reject) => {
    const options = {
      hostname: 'localhost',
      port: 3000,
      path: path,
      method: method,
      headers: {
        'Content-Type': 'application/json',
      }
    };

    if (postData) {
      options.headers['Content-Length'] = Buffer.byteLength(postData);
    }

    const req = http.request(options, (res) => {
      console.log(`${method} ${path} - Status: ${res.statusCode}`);

      let data = '';
      res.on('data', (chunk) => {
        data += chunk;
      });

      res.on('end', () => {
        try {
          const jsonData = JSON.parse(data);
          resolve(jsonData);
        } catch (e) {
          resolve(data);
        }
      });
    });

    req.on('error', (error) => {
      reject(error);
    });

    if (postData) {
      req.write(postData);
    }
    req.end();
  });
}

async function testAPI() {
  try {
    // Start a new research request
    console.log('Starting new research request...');
    const postData = JSON.stringify({
      query: 'What are the latest developments in AI safety research?',
      breadth: 2,
      depth: 1
    });

    const startResponse = await makeRequest('/research', 'POST', postData);
    console.log('Start Response:', JSON.stringify(startResponse, null, 2));

    if (startResponse.requestId) {
      const requestId = startResponse.requestId;
      console.log(`\nWaiting a moment, then checking status for request ID: ${requestId}`);

      // Wait a few seconds for the process to start
      await new Promise(resolve => setTimeout(resolve, 3000));

      const statusResponse = await makeRequest(`/research/status?requestId=${requestId}`);
      console.log('Status Response:', JSON.stringify(statusResponse, null, 2));

      // If completed, get the report
      if (statusResponse.status === 'completed') {
        console.log('\nResearch completed! Getting report...');
        const reportResponse = await makeRequest(`/research/report?requestId=${requestId}`);
        console.log('Report Response:', JSON.stringify(reportResponse, null, 2));
      } else {
        console.log('\nResearch still in progress. You can check status later with:');
        console.log(`curl "http://localhost:3000/research/status?requestId=${requestId}"`);
      }
    }

  } catch (error) {
    console.error('Error testing API:', error);
  }
}

testAPI();
