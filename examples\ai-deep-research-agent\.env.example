# ===== OPENAI CONFIGURATION =====
# OpenAI API key for generating queries, analyzing content, and creating reports
OPENAI_API_KEY=your-openai-api-key-here

# Optional: OpenAI model to use (defaults to gpt-4o)
# OPENAI_MODEL=gpt-4-turbo

# ===== CUSTOM OPENAI-COMPATIBLE API CONFIGURATION =====
# For using local LLMs, Ollama, or other OpenAI-compatible providers
# Uncomment and configure these options to use custom APIs instead of OpenAI

# Custom base URL for OpenAI-compatible API (e.g., local Ollama instance)
# OPENAI_BASE_URL=http://localhost:11434/v1

# Custom model name for the API provider
# OPENAI_MODEL=llama3.1:8b

# Optional: Organization ID (if required by your provider)
# OPENAI_ORGANIZATION=your-org-id

# Optional: Project ID (if required by your provider)
# OPENAI_PROJECT=your-project-id

# Optional: Additional headers as <PERSON><PERSON><PERSON> string (e.g., for authentication)
# OPENAI_DEFAULT_HEADERS={"Authorization": "Bearer your-token", "X-Custom-Header": "value"}

# ===== FIRECRAWL CONFIGURATION =====
# Firecrawl API key for web search and content extraction (required for hosted service)
FIRECRAWL_API_KEY=your-firecrawl-api-key-here

# Optional: Firecrawl base URL if using a self-hosted instance
# FIRECRAWL_API_URL=http://your-firecrawl-instance-url

# ===== LOCAL FIRECRAWL CONFIGURATION =====
# Set to true to use local Firecrawl instance (API key becomes optional)
# FIRECRAWL_LOCAL_MODE=true

# Local Firecrawl instance URL (defaults to http://localhost:3002 in local mode)
# FIRECRAWL_API_URL=http://localhost:3002

# Optional: Concurrency limit for batch content extraction (defaults to 2)
# FIRECRAWL_CONCURRENCY_LIMIT=2